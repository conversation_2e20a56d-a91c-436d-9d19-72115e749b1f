"use client"

import { But<PERSON> } from "@/components/ui/button"
import { ExternalLink, BarChart3 } from "lucide-react"

interface HeaderProps {
  title: string
  description: string
  onExternalLinkClick?: () => void
}

export function Header({ title, description, onExternalLinkClick }: HeaderProps) {
  return (
    <div className="flex items-start justify-between">
      <div className="flex items-center gap-4">
        <div className="w-16 h-16 bg-[#6475e9] rounded-full flex items-center justify-center">
          <img
            src="/placeholder-icon-logo.png"
            alt="Logo"
            className="w-8 h-8 object-contain"
          />
        </div>
        <div>
          <h1 className="text-2xl font-semibold text-[#1e1e1e]">{title}</h1>
          <p className="text-[#64707d] text-sx mt-1">{description}</p>
        </div>
      </div>
      <Button variant="outline" size="icon" className="border-[#d3d3d3] bg-transparent" onClick={onExternalLinkClick}>
        <ExternalLink className="w-4 h-4" />
      </Button>
    </div>
  )
}

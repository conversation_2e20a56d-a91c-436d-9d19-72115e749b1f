'use client';

import { useRouter } from 'next/navigation';

interface AssessmentHeaderProps {
  currentQuestion?: number;
  totalQuestions?: number;
  assessmentName?: string;
  phase?: string;
}

export default function AssessmentHeader({
  currentQuestion = 1,
  totalQuestions = 44,
  assessmentName = "Big Five Personality",
  phase = "Phase 1"
}: AssessmentHeaderProps) {
  const router = useRouter();

  const handleBackToDashboard = () => {
    router.push('/');
  };

  return (
    <div className="flex items-center justify-between px-8 py-6 bg-transparent">
      <div className="flex items-center gap-2">
        <button
          onClick={handleBackToDashboard}
          className="flex items-center gap-2 px-4 py-2 rounded-full border border-[#E5E7EB] bg-white text-[#64707D] text-[16px] font-medium shadow-sm hover:bg-[#f5f7fb] transition"
          type="button"
        >
          <img src="/icons/CaretLeft.svg" alt="Back" className="w-4 h-4" />
          Kembali ke Dashboard
        </button>
        <span className="font-semibold text-lg ml-4">{phase}: {assessmentName}</span>
      </div>
      <div className="flex items-center gap-6">
        <span className="text-sm text-[#64707d]">Pertanyaan {currentQuestion} dari {totalQuestions}</span>
        <button className="text-[#6475e9] text-sm font-semibold">Simpan & Keluar</button>
      </div>
    </div>
  );
}
